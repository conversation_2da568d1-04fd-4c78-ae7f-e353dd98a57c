// Base URL for assets
const baseUrl = window.location.href.substring(0, window.location.href.lastIndexOf('/') + 1);
console.log(`Base URL: ${baseUrl}`);

// Initialize Fabric <PERSON>vas
const canvas = new fabric.Canvas('strategy-canvas', {
    isDrawingMode: false,
    width: window.innerWidth - 180,
    height: window.innerHeight - 180,
    selection: true, // Enable object selection by default
    preserveObjectStacking: true, // Maintain z-index of objects
    imageSmoothingEnabled: true, // Enable image smoothing
    centeredScaling: true, // Scale from center
    centeredRotation: true, // Rotate from center
    renderOnAddRemove: true, // Render on object add/remove
    stateful: true // Keep state of objects
});

// Position the canvas viewport in the center initially
canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);

// Global variables
let currentTool = 'select';
let currentMap = 'Ascent';
let currentColor = '#FF4655';
let currentBrushSize = 3;
let agentIcons = [];
let undoStack = [];
let redoStack = [];
let mapBasePath = ''; // Will store the correct map directory path
let agentBasePath = ''; // Will store the correct agent icons path

// Collaboration variables
let webrtcManager = null;
let collaborationManager = null;
let isCollaborating = false;

// DOM Elements
const mapSelect = document.getElementById('map-select');
const agentIconsContainer = document.getElementById('agent-icons');
const toolButtons = document.querySelectorAll('.tool-btn');
const colorSelect = document.getElementById('color-select');
const brushSizeInput = document.getElementById('brush-size');
const clearBtn = document.getElementById('clear-btn');
const undoBtn = document.getElementById('undo-btn');
const redoBtn = document.getElementById('redo-btn');
const saveBtn = document.getElementById('save-btn');
const loadBtn = document.getElementById('load-btn');
const strategyNameInput = document.getElementById('strategy-name');
const loadModal = document.getElementById('load-modal');
const closeModal = document.querySelector('.close');
const savedStrategiesList = document.getElementById('saved-strategies-list');

// Initialize zoom-related variables
let zoomLevel = 1;
const MAX_ZOOM = 3;
const MIN_ZOOM = 0.5;

// Initialize the app
function init() {
    console.log("Initializing Valorant Strategy Board app...");

    // Check for mobile and show warning if needed
    checkMobileAndShowWarning();

    // Set the asset paths directly
    mapBasePath = './mini-map/';
    agentBasePath = './agent-icons/';

    // Set default drawing color to white
    currentColor = '#FFFFFF';
    colorSelect.value = '#FFFFFF';

    // Set initial map selection value
    const mapSelectElement = document.getElementById('map-select');
    if (mapSelectElement) {
        // Get the selected map from dropdown
        currentMap = mapSelectElement.value;
        console.log(`Initial map selected: ${currentMap}`);
    } else {
        console.error("Map select element not found");
    }

    // Set up panning functionality
    setupPanning();

    // Resize canvas on window resize
    window.addEventListener('resize', function() {
        resizeCanvas();
        // Re-center the map after resize
        if (canvas.backgroundImage) {
            centerBackgroundImage();
        }
    });

    // Set up zoom handlers
    setupZoomHandlers();

    // Set up keyboard shortcuts
    setupKeyboardShortcuts();

    // Load the initial map
    loadMap(currentMap);

    // Load agent icons
    loadAgentIcons();

    // Set up event listeners
    setupEventListeners();

    // Initialize collaboration
    initializeCollaboration();

    // Save initial canvas state for undo
    saveCanvasState();
}

// Check if mobile and show warning
function checkMobileAndShowWarning() {
    const isMobile = window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    if (isMobile) {
        const mobileWarning = document.getElementById('mobile-warning');
        const continueBtn = document.getElementById('continue-mobile');

        mobileWarning.classList.add('show');

        continueBtn.addEventListener('click', () => {
            mobileWarning.classList.remove('show');
            // Store preference to not show again this session
            sessionStorage.setItem('mobile-warning-dismissed', 'true');
        });

        // Auto-hide after 10 seconds
        setTimeout(() => {
            if (mobileWarning.classList.contains('show')) {
                mobileWarning.classList.remove('show');
            }
        }, 10000);
    }
}

// Center the background image on the canvas
function centerBackgroundImage() {
    if (!canvas.backgroundImage) return;

    // Position the background image in the center
    canvas.backgroundImage.set({
        left: canvas.width / 2,
        top: canvas.height / 2,
        originX: 'center',
        originY: 'center'
    });

    canvas.renderAll();
}

// Resize canvas when window changes size
function resizeCanvas() {
    console.log(`Resizing canvas to: ${window.innerWidth - 180}x${window.innerHeight - 180}`);

    // Set canvas dimensions
    canvas.setWidth(window.innerWidth - 180);
    canvas.setHeight(window.innerHeight - 180);

    // Re-center all content
    centerContent();

    // Re-render canvas
    canvas.renderAll();
}

// Center all content on the canvas
function centerContent() {
    // Center the background image if it exists
    if (canvas.backgroundImage) {
        canvas.backgroundImage.set({
            left: canvas.width / 2,
            top: canvas.height / 2
        });
    }

    // Possibly adjust other elements as needed
}

// Function to get full asset URL
function getAssetUrl(path) {
    // Normalize path
    path = path.startsWith('./') ? path.substring(2) : path;
    return baseUrl + path;
}

// Load map as canvas background
function loadMap(mapName) {
    // Use exactly the filenames as they appear in the directory
    let formattedMapName;

    switch(mapName) {
        case 'Ascent':
            formattedMapName = 'Ascent mini map.png';
            break;
        case 'Bind':
            formattedMapName = 'Bind mini map.png';
            break;
        case 'Haven':
            formattedMapName = 'haven mini map.png';
            break;
        case 'Split':
            formattedMapName = 'Split mini map.png';
            break;
        case 'Icebox':
            formattedMapName = 'ice box mini map.png';
            break;
        case 'Pearl':
            formattedMapName = 'Pearl mini map.png';
            break;
        case 'Fracture':
            formattedMapName = 'fracture mini map.png';
            break;
        case 'Lotus':
            formattedMapName = 'lotus mini map.png';
            break;
        case 'Sunset':
            formattedMapName = 'sunsetmini map.png';
            break;
        case 'Abyss':
            formattedMapName = 'abyss mini map.png';
            break;
        default:
            formattedMapName = 'haven mini map.png';
    }

    // Build the full path
    const fullImagePath = mapBasePath + formattedMapName;
    console.log(`Loading map from: ${fullImagePath}`);

    // Use direct DOM Image element for reliable loading
    const imgElement = new Image();

    // Set up load event handler
    imgElement.onload = function() {
        console.log(`Map image loaded successfully: ${formattedMapName}`);
        console.log(`Image dimensions: ${imgElement.width}x${imgElement.height}`);

        // Create fabric image from the loaded DOM image
        const fabricImage = new fabric.Image(imgElement);

        // Clear canvas first and reset viewport
        canvas.clear();
        canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);
        zoomLevel = 1;

        // Calculate the scale to fit the map properly
        const scaleX = (canvas.width * 0.95) / imgElement.width;
        const scaleY = (canvas.height * 0.95) / imgElement.height;
        const scale = Math.min(scaleX, scaleY);

        // Scale the image
        fabricImage.scale(scale);

        // Set as background image
        canvas.setBackgroundImage(fabricImage, function() {
            // Center the background image
            fabricImage.center();
            fabricImage.setCoords();

            // Make background image non-interactive
            fabricImage.selectable = false;
            fabricImage.evented = false;

            // Render the canvas
            canvas.renderAll();
            console.log("Canvas rendered with background image centered");

            // Save initial state for undo
            saveCanvasState();
        }, {
            // Ensure the background is properly stretched and positioned
            originX: 'center',
            originY: 'center',
            crossOrigin: 'anonymous'
        });
    };

    // Error handler
    imgElement.onerror = function(e) {
        console.error(`Failed to load map image: ${formattedMapName}`, e);

        // Show a visible error on the canvas
        const text = new fabric.Text(`Could not load map: ${formattedMapName}\nCheck console for details`, {
            left: canvas.width / 2,
            top: canvas.height / 2,
            originX: 'center',
            originY: 'center',
            fill: 'red',
            fontSize: 20
        });

        canvas.clear();
        canvas.add(text);
        canvas.renderAll();
    };

    // Set crossOrigin to anonymous to prevent CORS issues
    imgElement.crossOrigin = 'anonymous';

    // Set the source to trigger loading
    imgElement.src = fullImagePath;
}

// Load agent icons
function loadAgentIcons() {
    // List of agent names based on the files we have
    const agents = [
    'astra', 'breach', 'brim', 'chamber', 'clove', 'cypher', 'deadlock', 'fade', 
    'fnix', 'gekko', 'harbour', 'iso', 'jett', 'kayo', 'killjoy', 'neon', 
    'omen', 'raze', 'reyna', 'sage', 'skye', 'sova', 'tejo', 'viper', 
    'vyse', 'waylay', 'yoru'
];

    // Clear the container
    agentIconsContainer.innerHTML = '';

    console.log(`Loading agent icons from: ${agentBasePath}`);

    // Create agent icons
    agents.forEach(agent => {
        // Format filename exactly as they appear in the directory listing
        let fileName;

        if (agent === 'sage') {
            fileName = 'sageMini map icon.png';
        } else if (agent === 'viper') {
            fileName = 'viperMini map icon.png';
        } else {
            fileName = `${agent} Mini map icon.png`;
        }

        // Build absolute path for the icon using the detected base path
        const iconPath = agentBasePath + fileName;
        console.log(`Loading agent icon: ${iconPath}`);

        const iconImg = document.createElement('img');
        iconImg.src = iconPath;
        iconImg.alt = agent;
        iconImg.className = 'agent-icon';
        iconImg.dataset.agent = agent;

        // Add error handler to log issues
        iconImg.onerror = function() {
            console.error(`Failed to load agent icon: ${fileName}`);
        };

        // Make agent icon draggable
        iconImg.addEventListener('mousedown', function(e) {
            e.preventDefault();
            addAgentToCanvas(agent, fileName);
        });

        agentIconsContainer.appendChild(iconImg);
    });
}

// Ensure all agent icons stay on top of drawings
function ensureAgentsOnTop() {
    const agents = canvas.getObjects().filter(obj => obj.isAgent);
    agents.forEach(agent => {
        canvas.bringToFront(agent);
    });
    canvas.renderAll();
}

// Add agent to canvas when icon is clicked
function addAgentToCanvas(agentName, fileName) {
    const iconPath = agentBasePath + fileName;
    console.log(`Adding agent to canvas: ${agentName}, file: ${iconPath}`);

    fabric.Image.fromURL(iconPath, function(agentImg) {
        if (!agentImg) {
            console.error(`Failed to load agent image for canvas: ${fileName}`);
            return;
        }

        // Get the current pointer position for placement
        const pointer = canvas.getPointer(canvas.lastPosX || {x: canvas.width/2, y: canvas.height/2});

        // Scale down the agent icon - base scale doesn't need to account for zoom
        // Fabric.js will handle the display scaling automatically with the zoom
        const baseScale = 0.15;

        // Set agent properties
        agentImg.set({
            left: pointer.x,
            top: pointer.y,
            originX: 'center',
            originY: 'center',
            scaleX: baseScale,
            scaleY: baseScale,
            hasControls: true,
            hasBorders: true,
            cornerSize: 12,
            transparentCorners: false,
            name: agentName,
            isAgent: true
        });

        // Add to canvas
        canvas.add(agentImg);

        // Bring agent to front so it's always selectable
        canvas.bringToFront(agentImg);
        canvas.setActiveObject(agentImg);

        // Ensure agents stay on top when canvas is rendered
        ensureAgentsOnTop();

        // Save state for undo
        saveCanvasState();
    }, null, {crossOrigin: 'Anonymous'});
}

// Set up event listeners
function setupEventListeners() {
    // Map selection
    mapSelect.addEventListener('change', function() {
        currentMap = this.value;
        loadMap(currentMap);

        // Broadcast map change if collaborating
        if (isCollaborating && collaborationManager) {
            collaborationManager.broadcastMapChange(currentMap);
        }
    });

    // Tool buttons
    toolButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            // Remove active class from all tool buttons
            toolButtons.forEach(b => b.classList.remove('active'));

            // Add active class to clicked button
            this.classList.add('active');

            // Update current tool
            currentTool = this.dataset.tool;

            // Update canvas drawing mode
            updateCanvasToolMode();
        });
    });

    // Add delete button to the UI
    const drawingTools = document.querySelector('.drawing-tools');
    if (drawingTools) {
        const deleteBtn = document.createElement('button');
        deleteBtn.id = 'delete-btn';
        deleteBtn.className = 'tool-btn';
        deleteBtn.innerHTML = '<span class="tooltip">Delete Selected (Del)</span>🗑️';
        deleteBtn.title = 'Delete Selected (Del)';
        drawingTools.appendChild(deleteBtn);

        // Add click event to delete button
        deleteBtn.addEventListener('click', function() {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                canvas.remove(activeObject);
                canvas.discardActiveObject();
                canvas.renderAll();
                saveCanvasState();
            }
        });
    }

    // Color selection
    colorSelect.addEventListener('change', function() {
        currentColor = this.value;
        updateCanvasToolMode();
    });

    // Brush size
    brushSizeInput.addEventListener('input', function() {
        currentBrushSize = parseInt(this.value);
        updateCanvasToolMode();
    });

    // Clear button
    clearBtn.addEventListener('click', function() {
        if (confirm('Are you sure you want to clear the canvas?')) {
            // Save state before clearing
            saveCanvasState();

            // Clear canvas but keep background
            const bgImage = canvas.backgroundImage;
            canvas.clear();
            if (bgImage) {
                canvas.setBackgroundImage(bgImage, canvas.renderAll.bind(canvas));
            }

            // Save the cleared state
            saveCanvasState();
        }
    });

    // Track mouse position for agent placement
    canvas.on('mouse:move', function(options) {
        canvas.lastPosX = options.e;
    });

    // Undo button
    undoBtn.addEventListener('click', undo);

    // Redo button
    redoBtn.addEventListener('click', redo);

    // Save button
    saveBtn.addEventListener('click', saveStrategy);

    // Load button
    loadBtn.addEventListener('click', openLoadModal);

    // Close modal
    closeModal.addEventListener('click', function() {
        loadModal.style.display = 'none';
    });

    // Click outside modal to close
    window.addEventListener('click', function(e) {
        if (e.target === loadModal) {
            loadModal.style.display = 'none';
        }
    });

    // Canvas object events
    canvas.on('object:added', function(options) {
        // Make sure we're not in the middle of loading a saved state
        if (!canvas.isLoadingFromJSON) {
            // Mark new path objects as drawing paths
            if (options.target && options.target.type === 'path') {
                options.target.isDrawingPath = true;
            }
            saveCanvasState();
        }
    });

    canvas.on('object:modified', function() {
        if (!canvas.isLoadingFromJSON) {
            saveCanvasState();
        }
    });

    canvas.on('path:created', function(options) {
        if (options.path) {
            // Mark the path as a drawing path
            options.path.isDrawingPath = true;
        }
        if (!canvas.isLoadingFromJSON) {
            ensureAgentsOnTop(); // Keep agents on top after drawing
            saveCanvasState();
        }
    });
}

// Initialize collaboration features
function initializeCollaboration() {
    // Initialize WebSocket manager (replaces WebRTC)
    webrtcManager = new WebSocketManager();

    // Initialize collaboration manager
    collaborationManager = new CollaborationManager(canvas, webrtcManager);

    // Set up collaboration UI event listeners
    setupCollaborationEventListeners();

    console.log('Collaboration features initialized');
}

// Set up collaboration event listeners
function setupCollaborationEventListeners() {
    const collaborateBtn = document.getElementById('collaborate-btn');
    const collaborationModal = document.getElementById('collaboration-modal');
    const collabModalClose = document.getElementById('collab-modal-close');
    const hostSessionBtn = document.getElementById('host-session-btn');
    const joinSessionBtn = document.getElementById('join-session-btn');

    // Collaborate button click
    collaborateBtn.addEventListener('click', () => {
        // If already collaborating, show disconnect option
        if (isCollaborating) {
            const disconnect = confirm('Do you want to disconnect from the collaboration session?');
            if (disconnect) {
                collaborationManager.disconnect();
                updateCollaborateButton('offline');
                isCollaborating = false;
            }
        } else {
            collaborationModal.style.display = 'block';
            resetCollaborationModal();
        }
    });

    // Close modal
    collabModalClose.addEventListener('click', () => {
        collaborationModal.style.display = 'none';
    });

    // Click outside modal to close
    window.addEventListener('click', (e) => {
        if (e.target === collaborationModal) {
            collaborationModal.style.display = 'none';
        }
    });

    // Host session button
    hostSessionBtn.addEventListener('click', () => {
        showHostPanel();
    });

    // Join session button
    joinSessionBtn.addEventListener('click', () => {
        showJoinPanel();
    });

    // Set up host panel event listeners
    setupHostPanelListeners();

    // Set up join panel event listeners
    setupJoinPanelListeners();

    // Set up session info copy button
    const copyCurrentCodeBtn = document.getElementById('copy-current-code');
    if (copyCurrentCodeBtn) {
        copyCurrentCodeBtn.addEventListener('click', () => {
            const sessionCode = document.getElementById('current-session-code').textContent;
            copyToClipboard(sessionCode, 'Session code copied!');
        });
    }
}

// Update canvas tool mode based on selected tool - IMPROVED VERSION
function updateCanvasToolMode() {
    // First, reset everything to default state
    canvas.isDrawingMode = false;
    canvas.selection = true;
    canvas.forEachObject(function(obj) {
        obj.selectable = true;
        obj.evented = true;
    });

    // Remove canvas drawing mode class
    const canvasContainer = document.querySelector('.canvas-container');
    canvasContainer.classList.remove('drawing-mode');

    // Remove all event listeners to ensure clean state
    canvas.off('mouse:down');
    canvas.off('mouse:move');
    canvas.off('mouse:up');

    // Clean up eraser if it was previously active
    if (currentTool === 'eraser') {
        cleanupEraserTool();
    }

    // Then apply specific tool settings
    switch (currentTool) {
        case 'select':
            // Already set defaults above
            break;

        case 'pencil':
            canvas.isDrawingMode = true;
            canvasContainer.classList.add('drawing-mode');
            canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
            canvas.freeDrawingBrush.color = currentColor;
            canvas.freeDrawingBrush.width = currentBrushSize;
            canvas.freeDrawingBrush.strokeLineCap = 'round';
            canvas.freeDrawingBrush.strokeLineJoin = 'round';

            // Create path objects that are drawn completely (with proper coordinates)
            canvas.freeDrawingBrush.convertToObject = function() {
                const o = fabric.util.object.clone(this.callSuper('convertToObject'));
                o.isDrawingPath = true;
                return o;
            };

            break;

        case 'line':
            canvas.selection = false;
            canvasContainer.classList.add('drawing-mode');
            canvas.forEachObject(function(obj) {
                obj.selectable = false;
            });
            enableLineDrawing();
            break;

        case 'arrow':
            canvas.selection = false;
            canvasContainer.classList.add('drawing-mode');
            canvas.forEachObject(function(obj) {
                obj.selectable = false;
            });
            enableArrowDrawing();
            break;

        case 'rect':
            canvas.selection = false;
            canvasContainer.classList.add('drawing-mode');
            canvas.forEachObject(function(obj) {
                obj.selectable = false;
            });
            enableRectDrawing();
            break;

        case 'circle':
            canvas.selection = false;
            canvasContainer.classList.add('drawing-mode');
            canvas.forEachObject(function(obj) {
                obj.selectable = false;
            });
            enableCircleDrawing();
            break;

        case 'eraser':
            canvas.isDrawingMode = false;
            canvas.selection = false;
            canvasContainer.classList.add('drawing-mode');
            canvas.forEachObject(function(obj) {
                obj.selectable = false;
                obj.evented = false;
            });
            enableEraserTool();
            break;
    }
}

// Line drawing functionality
function enableLineDrawing() {
    let line, isDown;

    canvas.off('mouse:down');
    canvas.off('mouse:move');
    canvas.off('mouse:up');

    canvas.on('mouse:down', function(o) {
        isDown = true;
        const pointer = canvas.getPointer(o.e);
        const points = [pointer.x, pointer.y, pointer.x, pointer.y];

        line = new fabric.Line(points, {
            strokeWidth: currentBrushSize,
            stroke: currentColor,
            originX: 'center',
            originY: 'center',
            selectable: true,
            evented: true
        });

        canvas.add(line);
        canvas.bringToFront(line);
    });

    canvas.on('mouse:move', function(o) {
        if (!isDown) return;

        const pointer = canvas.getPointer(o.e);
        line.set({ x2: pointer.x, y2: pointer.y });
        canvas.renderAll();
    });

    canvas.on('mouse:up', function() {
        isDown = false;
        line.setCoords();
        ensureAgentsOnTop(); // Keep agents on top
        saveCanvasState();
    });
}

// Arrow drawing functionality
function enableArrowDrawing() {
    let arrow, isDown;

    canvas.off('mouse:down');
    canvas.off('mouse:move');
    canvas.off('mouse:up');

    canvas.on('mouse:down', function(o) {
        isDown = true;
        const pointer = canvas.getPointer(o.e);

        // Create a line for the arrow shaft
        const points = [pointer.x, pointer.y, pointer.x, pointer.y];
        arrow = new fabric.Line(points, {
            strokeWidth: currentBrushSize,
            stroke: currentColor,
            originX: 'center',
            originY: 'center',
            selectable: true,
            evented: true
        });

        canvas.add(arrow);
        canvas.bringToFront(arrow);
    });

    canvas.on('mouse:move', function(o) {
        if (!isDown) return;

        const pointer = canvas.getPointer(o.e);
        arrow.set({ x2: pointer.x, y2: pointer.y });
        canvas.renderAll();
    });

    canvas.on('mouse:up', function() {
        if (!isDown) return;

        isDown = false;

        // Get the arrow's endpoints
        const x1 = arrow.x1;
        const y1 = arrow.y1;
        const x2 = arrow.x2;
        const y2 = arrow.y2;

        // Calculate the angle for the arrowhead
        const angle = Math.atan2(y2 - y1, x2 - x1);

        // Create arrowhead
        const headLength = 15;
        const headAngle = 25 * (Math.PI / 180); // 25 degrees in radians

        // Calculate arrowhead points
        const topX = x2 - headLength * Math.cos(angle - headAngle);
        const topY = y2 - headLength * Math.sin(angle - headAngle);
        const bottomX = x2 - headLength * Math.cos(angle + headAngle);
        const bottomY = y2 - headLength * Math.sin(angle + headAngle);

        // Create arrowhead using a triangle
        const arrowHead = new fabric.Triangle({
            left: x2,
            top: y2,
            width: headLength,
            height: headLength,
            fill: currentColor,
            stroke: currentColor,
            angle: (angle * 180 / Math.PI) + 90,
            originX: 'center',
            originY: 'center',
            selectable: true,
            evented: true
        });

        // Group the line and arrowhead
        const group = new fabric.Group([arrow, arrowHead], {
            selectable: true,
            name: 'arrow'
        });

        // Remove the original line
        canvas.remove(arrow);

        // Add the group
        canvas.add(group);
        canvas.bringToFront(group);
        ensureAgentsOnTop(); // Keep agents on top
        saveCanvasState();
    });
}

// Rectangle drawing functionality
function enableRectDrawing() {
    let rect, isDown, origX, origY;

    canvas.off('mouse:down');
    canvas.off('mouse:move');
    canvas.off('mouse:up');

    canvas.on('mouse:down', function(o) {
        isDown = true;
        const pointer = canvas.getPointer(o.e);
        origX = pointer.x;
        origY = pointer.y;

        rect = new fabric.Rect({
            left: origX,
            top: origY,
            originX: 'left',
            originY: 'top',
            width: 0,
            height: 0,
            fill: 'transparent',
            stroke: currentColor,
            strokeWidth: currentBrushSize,
            selectable: true,
            evented: true
        });

        canvas.add(rect);
        canvas.bringToFront(rect);
    });

    canvas.on('mouse:move', function(o) {
        if (!isDown) return;

        const pointer = canvas.getPointer(o.e);

        if (pointer.x < origX) {
            rect.set({ left: pointer.x });
        }

        if (pointer.y < origY) {
            rect.set({ top: pointer.y });
        }

        rect.set({
            width: Math.abs(pointer.x - origX),
            height: Math.abs(pointer.y - origY)
        });

        canvas.renderAll();
    });

    canvas.on('mouse:up', function() {
        isDown = false;
        rect.setCoords();
        ensureAgentsOnTop(); // Keep agents on top
        saveCanvasState();
    });
}

// Circle drawing functionality
function enableCircleDrawing() {
    let circle, isDown, origX, origY;

    canvas.off('mouse:down');
    canvas.off('mouse:move');
    canvas.off('mouse:up');

    canvas.on('mouse:down', function(o) {
        isDown = true;
        const pointer = canvas.getPointer(o.e);
        origX = pointer.x;
        origY = pointer.y;

        circle = new fabric.Circle({
            left: origX,
            top: origY,
            originX: 'left',
            originY: 'top',
            radius: 0,
            fill: 'transparent',
            stroke: currentColor,
            strokeWidth: currentBrushSize,
            selectable: true,
            evented: true
        });

        canvas.add(circle);
        canvas.bringToFront(circle);
    });

    canvas.on('mouse:move', function(o) {
        if (!isDown) return;

        const pointer = canvas.getPointer(o.e);
        const radius = Math.sqrt(
            Math.pow(pointer.x - origX, 2) +
            Math.pow(pointer.y - origY, 2)
        ) / 2;

        // Set center coordinates
        const centerX = (origX + pointer.x) / 2;
        const centerY = (origY + pointer.y) / 2;

        circle.set({
            left: centerX,
            top: centerY,
            originX: 'center',
            originY: 'center',
            radius: radius
        });

        canvas.renderAll();
    });

    canvas.on('mouse:up', function() {
        isDown = false;
        circle.setCoords();
        ensureAgentsOnTop(); // Keep agents on top
        saveCanvasState();
    });
}

// Custom eraser functionality that only erases drawings, not agents
function enableEraserTool() {
    let isDown = false;
    const eraserSize = currentBrushSize * 3; // Make eraser slightly larger for easier use

    // Create a visual indicator for the eraser
    let eraserIndicator = new fabric.Circle({
        radius: eraserSize / 2,
        fill: 'rgba(255,0,0,0.3)', // Red with transparency for better visibility
        stroke: 'white',
        strokeWidth: 2,
        originX: 'center',
        originY: 'center',
        left: 0,
        top: 0,
        selectable: false,
        evented: false,
        excludeFromExport: true
    });

    // Disable any active selection
    canvas.discardActiveObject();
    canvas.selection = false;

    // Make all objects non-selectable to prevent accidental selection
    canvas.forEachObject(function(obj) {
        obj.selectable = false;
        obj.evented = false;
    });

    // Reset all event handlers
    canvas.off('mouse:down');
    canvas.off('mouse:move');
    canvas.off('mouse:up');

    // Update eraser indicator position on mouse move
    canvas.on('mouse:move', function(opt) {
        const pointer = canvas.getPointer(opt.e);

        // Update or add the indicator to show eraser position
        if (!canvas.contains(eraserIndicator)) {
            canvas.add(eraserIndicator);
            canvas.bringToFront(eraserIndicator);
        }

        eraserIndicator.set({
            left: pointer.x,
            top: pointer.y
        });
        canvas.renderAll();

        // If mouse is down, check for objects to erase
        if (isDown) {
            eraseObjectsUnderCursor(pointer);
        }
    });

    // Start erasing on mouse down
    canvas.on('mouse:down', function(opt) {
        // Prevent default browser behavior
        opt.e.preventDefault();
        opt.e.stopPropagation();

        isDown = true;
        const pointer = canvas.getPointer(opt.e);

        // Immediately try to erase on mouse down
        eraseObjectsUnderCursor(pointer);
    });

    // Stop erasing on mouse up
    canvas.on('mouse:up', function(opt) {
        // Prevent default browser behavior
        if (opt && opt.e) {
            opt.e.preventDefault();
            opt.e.stopPropagation();
        }

        isDown = false;
        // Save state after erasing
        saveCanvasState();
    });

    // Function to erase objects under the cursor
    function eraseObjectsUnderCursor(pointer) {
        // Get all objects on canvas
        const objects = canvas.getObjects();
        let erasedSomething = false;

        // Check each object
        for (let i = objects.length - 1; i >= 0; i--) {
            const obj = objects[i];

            // Skip the eraser indicator itself and agent icons
            if (obj === eraserIndicator || obj.isAgent) {
                continue;
            }

            let shouldErase = false;

            if (obj.type === 'path') {
                // For paths (pencil strokes), check if path is close to pointer
                const objCenter = obj.getCenterPoint();
                const distance = Math.sqrt(
                    Math.pow(objCenter.x - pointer.x, 2) +
                    Math.pow(objCenter.y - pointer.y, 2)
                );

                // Use a slightly larger threshold for paths to make it easier to erase
                if (distance < eraserSize) {
                    shouldErase = true;
                }
            } else {
                // For other objects, use center point and size
                const objCenter = obj.getCenterPoint();
                const distance = Math.sqrt(
                    Math.pow(objCenter.x - pointer.x, 2) +
                    Math.pow(objCenter.y - pointer.y, 2)
                );

                // Determine object size (use the larger dimension)
                let objSize;
                if (obj.type === 'circle') {
                    objSize = obj.radius * obj.scaleX;
                } else if (obj.width && obj.height) {
                    objSize = Math.max(obj.width * obj.scaleX, obj.height * obj.scaleY) / 2;
                } else {
                    objSize = 20; // Default size if we can't determine
                }

                // If the eraser is close enough to the object, mark for removal
                if (distance < (eraserSize / 2) + objSize) {
                    shouldErase = true;
                }
            }

            // Erase the object if it should be erased
            if (shouldErase) {
                console.log(`Erasing object: ${obj.type}`);
                canvas.remove(obj);
                erasedSomething = true;
            }
        }

        // Only render if we actually removed something
        if (erasedSomething) {
            canvas.renderAll();
        }
    }

    // Clean up when switching tools
    function cleanupEraserTool() {
        if (canvas.contains(eraserIndicator)) {
            canvas.remove(eraserIndicator);
        }

        // Reset canvas selection mode
        canvas.selection = true;

        // Reset all objects to be selectable again
        canvas.forEachObject(function(obj) {
            // Make all objects selectable, not just agents
            obj.selectable = true;
            obj.evented = true;
        });

        canvas.renderAll();
    }

    // Listen for tool change to clean up
    toolButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            if (this.dataset.tool !== 'eraser') {
                cleanupEraserTool();
            }
        });
    });
}

// Save canvas state for undo/redo
function saveCanvasState() {
    // Limit stack size
    if (undoStack.length >= 20) {
        undoStack.shift();
    }

    // Save current state
    const json = JSON.stringify(canvas);
    undoStack.push(json);

    // Clear redo stack when a new action is performed
    redoStack = [];

    // Update button states
    updateUndoRedoButtons();
}

// Undo the last action
function undo() {
    if (undoStack.length > 1) {
        // Move current state to redo stack
        const current = undoStack.pop();
        redoStack.push(current);

        // Load previous state
        const previous = undoStack[undoStack.length - 1];
        loadCanvasState(previous);

        // Update button states
        updateUndoRedoButtons();
    }
}

// Redo the last undone action
function redo() {
    if (redoStack.length > 0) {
        // Get last redo state
        const next = redoStack.pop();

        // Add to undo stack
        undoStack.push(next);

        // Load the state
        loadCanvasState(next);

        // Update button states
        updateUndoRedoButtons();
    }
}

// Load a saved canvas state
function loadCanvasState(json) {
    // Preserve background image
    const bgImage = canvas.backgroundImage;

    // Set a flag to prevent triggering object:added events during loading
    canvas.isLoadingFromJSON = true;

    // Load the state
    canvas.loadFromJSON(json, function() {
        // Restore background if needed
        if (!canvas.backgroundImage && bgImage) {
            canvas.setBackgroundImage(bgImage, canvas.renderAll.bind(canvas));
        }

        // Mark all path objects as drawing paths for consistency
        canvas.forEachObject(function(obj) {
            if (obj.type === 'path') {
                obj.isDrawingPath = true;
            }
        });

        // Reset the loading flag
        canvas.isLoadingFromJSON = false;

        canvas.renderAll();
    });
}

// Update undo/redo button states
function updateUndoRedoButtons() {
    undoBtn.disabled = undoStack.length <= 1;
    redoBtn.disabled = redoStack.length === 0;
}

// Save strategy to localStorage
function saveStrategy() {
    const strategyName = strategyNameInput.value.trim();

    if (!strategyName) {
        alert('Please enter a strategy name.');
        return;
    }

    // Get current strategies from localStorage
    let strategies = JSON.parse(localStorage.getItem('valorantStrategies')) || {};

    // Add the new strategy
    strategies[strategyName] = {
        map: currentMap,
        canvas: JSON.stringify(canvas),
        date: new Date().toISOString()
    };

    // Save to localStorage
    localStorage.setItem('valorantStrategies', JSON.stringify(strategies));

    alert(`Strategy "${strategyName}" saved successfully!`);
}

// Open load modal
function openLoadModal() {
    // Populate saved strategies list
    populateSavedStrategies();

    // Display modal
    loadModal.style.display = 'block';
}

// Populate saved strategies in the load modal
function populateSavedStrategies() {
    // Clear the list
    savedStrategiesList.innerHTML = '';

    // Get strategies from localStorage
    const strategies = JSON.parse(localStorage.getItem('valorantStrategies')) || {};

    // Check if there are any saved strategies
    if (Object.keys(strategies).length === 0) {
        savedStrategiesList.innerHTML = '<div class="no-strategies">No saved strategies found.</div>';
        return;
    }

    // Add each strategy to the list
    for (const name in strategies) {
        const strategy = strategies[name];
        const mapName = strategy.map || 'Unknown';
        const date = new Date(strategy.date).toLocaleString();

        const item = document.createElement('div');
        item.className = 'strategy-item';
        item.innerHTML = `
            <div class="strategy-info">
                <div class="strategy-name">${name}</div>
                <div class="strategy-details">Map: ${mapName} | ${date}</div>
            </div>
            <span class="delete-strategy" data-name="${name}">❌</span>
        `;

        // Add click event to load the strategy
        item.querySelector('.strategy-info').addEventListener('click', function() {
            loadStrategy(name);
        });

        // Add click event to delete the strategy
        item.querySelector('.delete-strategy').addEventListener('click', function(e) {
            e.stopPropagation();
            deleteStrategy(name);
        });

        savedStrategiesList.appendChild(item);
    }
}

// Load a selected strategy
function loadStrategy(name) {
    // Get strategies from localStorage
    const strategies = JSON.parse(localStorage.getItem('valorantStrategies')) || {};

    // Check if the strategy exists
    if (!strategies[name]) {
        alert(`Strategy "${name}" not found.`);
        return;
    }

    // Get the strategy data
    const strategy = strategies[name];

    // Update current map in the dropdown
    mapSelect.value = strategy.map;
    currentMap = strategy.map;

    // Load the canvas state
    loadCanvasState(strategy.canvas);

    // Set the strategy name in the input
    strategyNameInput.value = name;

    // Close the modal
    loadModal.style.display = 'none';

    // Clear the undo/redo stacks
    undoStack = [];
    redoStack = [];

    // Save the current state
    saveCanvasState();
}

// Delete a strategy
function deleteStrategy(name) {
    if (confirm(`Are you sure you want to delete strategy "${name}"?`)) {
        // Get strategies from localStorage
        let strategies = JSON.parse(localStorage.getItem('valorantStrategies')) || {};

        // Delete the strategy
        delete strategies[name];

        // Save the updated strategies
        localStorage.setItem('valorantStrategies', JSON.stringify(strategies));

        // Refresh the list
        populateSavedStrategies();
    }
}

// Set up zoom handlers
function setupZoomHandlers() {
    // Mouse wheel zoom - make it work even without Ctrl key for easier use
    document.querySelector('.canvas-container').addEventListener('wheel', function(e) {
        // Always prevent the default scroll behavior
        e.preventDefault();

        // Only apply zoom if Ctrl is pressed or if we're in the canvas area
        // This makes it more intuitive while still allowing regular scrolling elsewhere
        const delta = e.deltaY;
        const zoom = delta > 0 ? 0.9 : 1.1; // Zoom out (0.9) or in (1.1)

        zoomCanvas(zoom, e);
    }, { passive: false });

    // Add zoom buttons to UI
    const footer = document.querySelector('footer');
    if (footer) {
        const zoomControls = document.createElement('div');
        zoomControls.className = 'zoom-controls';
        zoomControls.style.marginLeft = '10px';
        zoomControls.innerHTML = `
            <button id="zoom-in" title="Zoom In">+</button>
            <button id="zoom-out" title="Zoom Out">-</button>
            <button id="zoom-reset" title="Reset Zoom">Reset</button>
        `;

        const drawingTools = document.querySelector('.drawing-tools');
        if (drawingTools) {
            drawingTools.appendChild(zoomControls);

            // Add event listeners to buttons
            document.getElementById('zoom-in').addEventListener('click', function() {
                zoomCanvas(1.1);
            });

            document.getElementById('zoom-out').addEventListener('click', function() {
                zoomCanvas(0.9);
            });

            document.getElementById('zoom-reset').addEventListener('click', function() {
                resetZoom();
            });
        }
    }
}

// Zoom the canvas
function zoomCanvas(zoom, event) {
    // Calculate new zoom level
    const newZoomLevel = zoomLevel * zoom;

    // Check if within zoom limits
    if (newZoomLevel < MIN_ZOOM || newZoomLevel > MAX_ZOOM) {
        return;
    }

    // Update zoom level
    zoomLevel = newZoomLevel;

    // Calculate zoom point - use mouse position if available, otherwise center
    let point;
    if (event) {
        const pointer = canvas.getPointer(event);
        point = new fabric.Point(pointer.x, pointer.y);
    } else {
        point = new fabric.Point(canvas.width / 2, canvas.height / 2);
    }

    // Apply zoom transformation to the canvas
    canvas.zoomToPoint(point, zoomLevel);

    // Make sure all objects update their coordinates
    canvas.forEachObject(function(obj) {
        obj.setCoords();
    });

    // Render the updated canvas
    canvas.renderAll();

    console.log(`Zoomed to level: ${zoomLevel.toFixed(2)}`);
}

// Reset zoom to original level
function resetZoom() {
    // Reset zoom level to 1
    zoomLevel = 1;

    // Reset the viewport transform to default (no zoom, centered)
    canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);

    // Make sure all objects update their coordinates
    canvas.forEachObject(function(obj) {
        obj.setCoords();
    });

    // Re-center the canvas
    if (canvas.backgroundImage) {
        centerBackgroundImage();
    }

    // Re-render
    canvas.renderAll();

    console.log("Zoom reset to 1.0");
}

// Set up panning functionality
function setupPanning() {
    // Variable to track if we're currently panning
    let isPanning = false;
    let lastClientX = 0;
    let lastClientY = 0;

    // Get the canvas container element
    const canvasContainer = document.querySelector('.canvas-container');

    // Mouse down event - start panning
    canvasContainer.addEventListener('mousedown', function(e) {
        // Only trigger panning with middle mouse button or Alt+left click
        if (e.button === 1 || (e.altKey && e.button === 0)) {
            e.preventDefault();
            isPanning = true;
            canvasContainer.style.cursor = 'grabbing';
            lastClientX = e.clientX;
            lastClientY = e.clientY;
        }
    });

    // Mouse move event - perform panning
    window.addEventListener('mousemove', function(e) {
        if (!isPanning) return;

        e.preventDefault();

        // Calculate how far the mouse has moved
        const deltaX = e.clientX - lastClientX;
        const deltaY = e.clientY - lastClientY;

        // Update the last position
        lastClientX = e.clientX;
        lastClientY = e.clientY;

        // Get current viewport transform
        const vpt = canvas.viewportTransform;

        // Add the delta to the current position
        vpt[4] += deltaX;
        vpt[5] += deltaY;

        // Apply the transform and re-render
        canvas.setViewportTransform(vpt);
        canvas.requestRenderAll();
    });

    // Mouse up event - stop panning
    window.addEventListener('mouseup', function(e) {
        if (isPanning) {
            isPanning = false;
            canvasContainer.style.cursor = 'grab';
        }
    });

    // Mouse leave event - stop panning if mouse leaves window
    window.addEventListener('mouseleave', function() {
        if (isPanning) {
            isPanning = false;
            canvasContainer.style.cursor = 'grab';
        }
    });

    // Prevent context menu from appearing on right-click
    canvasContainer.addEventListener('contextmenu', function(e) {
        if (isPanning) {
            e.preventDefault();
        }
    });
}

// Set up keyboard shortcuts for tools
function setupKeyboardShortcuts() {
    // Create notification element
    const notificationEl = document.createElement('div');
    notificationEl.className = 'shortcut-notification';
    document.body.appendChild(notificationEl);

    // Function to show shortcut notification
    function showShortcutNotification(text) {
        notificationEl.textContent = text;
        notificationEl.classList.add('visible');

        // Hide after 1.5 seconds
        setTimeout(() => {
            notificationEl.classList.remove('visible');
        }, 1500);
    }

    // Keyboard shortcuts for tool selection
    window.addEventListener('keydown', function(e) {
        // Only process shortcuts if not in an input field or select dropdown
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.tagName === 'SELECT') {
            return;
        }

        // Debug logging
        console.log('Key pressed:', e.key, 'Target:', e.target.tagName, 'Active element:', document.activeElement.tagName);

        // Delete selected object with Delete or Backspace key
        if (e.key === 'Delete' || e.key === 'Backspace') {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                canvas.remove(activeObject);
                canvas.discardActiveObject();
                canvas.renderAll();
                saveCanvasState();
                showShortcutNotification('Object Deleted');
                return;
            }
        }

        // Find the matching tool button and click it
        let toolToSelect = null;
        let notificationText = '';

        switch (e.key.toLowerCase()) {
            case 'v': // Select tool (V for selection)
                toolToSelect = 'select';
                notificationText = 'Select Tool (V)';
                break;
            case 'p': // Pencil tool (P for pencil)
                toolToSelect = 'pencil';
                notificationText = 'Pencil Tool (P)';
                break;
            case 'l': // Line tool (L for line)
                toolToSelect = 'line';
                notificationText = 'Line Tool (L)';
                break;
            case 'a': // Arrow tool (A for arrow)
                toolToSelect = 'arrow';
                notificationText = 'Arrow Tool (A)';
                break;
            case 'r': // Rectangle tool (R for rectangle)
                toolToSelect = 'rect';
                notificationText = 'Rectangle Tool (R)';
                break;
            case 'c': // Circle tool (C for circle)
                toolToSelect = 'circle';
                notificationText = 'Circle Tool (C)';
                break;
            case 'e': // Eraser tool (E for eraser)
                toolToSelect = 'eraser';
                notificationText = 'Eraser Tool (E)';
                break;
            case 'z': // Undo (Ctrl+Z)
                if (e.ctrlKey) {
                    e.preventDefault();
                    undo();
                    showShortcutNotification('Undo (Ctrl+Z)');
                    return;
                }
                break;
            case 'y': // Redo (Ctrl+Y)
                if (e.ctrlKey) {
                    e.preventDefault();
                    redo();
                    showShortcutNotification('Redo (Ctrl+Y)');
                    return;
                }
                break;
            case '+': // Zoom in
            case '=': // Also for zoom in (without shift)
                if (e.ctrlKey) {
                    e.preventDefault();
                    zoomCanvas(1.1);
                    showShortcutNotification('Zoom In (Ctrl++)');
                    return;
                }
                break;
            case '-': // Zoom out
                if (e.ctrlKey) {
                    e.preventDefault();
                    zoomCanvas(0.9);
                    showShortcutNotification('Zoom Out (Ctrl+-)');
                    return;
                }
                break;
            case '0': // Reset zoom
                if (e.ctrlKey) {
                    e.preventDefault();
                    resetZoom();
                    showShortcutNotification('Reset Zoom (Ctrl+0)');
                    return;
                }
                break;
        }

        // If we found a tool to select, click its button
        if (toolToSelect) {
            e.preventDefault(); // Prevent default browser behavior
            e.stopPropagation(); // Stop event from bubbling

            const toolBtn = document.querySelector(`[data-tool="${toolToSelect}"]`);
            if (toolBtn) {
                // Remove active class from all tool buttons
                toolButtons.forEach(b => b.classList.remove('active'));

                // Add active class to the selected tool button
                toolBtn.classList.add('active');

                // Update current tool
                currentTool = toolToSelect;

                // Update canvas tool mode
                updateCanvasToolMode();

                // Show notification
                showShortcutNotification(notificationText);

                console.log('Tool changed to:', toolToSelect, 'via keyboard shortcut');
            }
        }
    });

    // Add tooltips to tool buttons to show shortcuts
    const toolShortcuts = {
        'select': 'Select (V)',
        'pencil': 'Pencil (P)',
        'line': 'Line (L)',
        'arrow': 'Arrow (A)',
        'rect': 'Rectangle (R)',
        'circle': 'Circle (C)',
        'eraser': 'Eraser (E)'
    };

    // Update tool button titles with keyboard shortcuts
    toolButtons.forEach(btn => {
        const tool = btn.dataset.tool;
        if (toolShortcuts[tool]) {
            btn.title = toolShortcuts[tool];
        }
    });
}

// Collaboration panel functions
function resetCollaborationModal() {
    // Hide all panels
    document.getElementById('collab-mode-selector').style.display = 'block';
    document.getElementById('host-panel').style.display = 'none';
    document.getElementById('join-panel').style.display = 'none';

    // Clear form fields
    document.getElementById('session-name').value = '';
    document.getElementById('host-session-code').value = '';
    document.getElementById('join-session-code').value = '';

    // Reset status messages
    document.getElementById('host-status').textContent = 'Setting up session...';
    document.getElementById('join-status').textContent = 'Enter the session code to connect';
}

function showHostPanel() {
    document.getElementById('collab-mode-selector').style.display = 'none';
    document.getElementById('host-panel').style.display = 'block';
    document.getElementById('join-panel').style.display = 'none';
}

function showJoinPanel() {
    document.getElementById('collab-mode-selector').style.display = 'none';
    document.getElementById('host-panel').style.display = 'none';
    document.getElementById('join-panel').style.display = 'block';
}

function setupHostPanelListeners() {
    const startHostingBtn = document.getElementById('start-hosting');
    const cancelHostBtn = document.getElementById('cancel-host');
    const copySessionCodeBtn = document.getElementById('copy-session-code');

    startHostingBtn.addEventListener('click', async () => {
        const sessionName = document.getElementById('session-name').value.trim() || 'Strategy Session';
        const hostStatus = document.getElementById('host-status');

        try {
            hostStatus.textContent = 'Creating session...';
            startHostingBtn.disabled = true;

            const sessionCode = await webrtcManager.createSession(sessionName);

            document.getElementById('host-session-code').value = sessionCode;
            hostStatus.textContent = 'Session created! Share the code with your team.';
            copySessionCodeBtn.disabled = false;

            // Update collaborate button
            updateCollaborateButton('hosting');
            isCollaborating = true;

        } catch (error) {
            console.error('Failed to create session:', error);
            hostStatus.textContent = 'Failed to create session. Please try again.';
            startHostingBtn.disabled = false;
        }
    });

    cancelHostBtn.addEventListener('click', () => {
        document.getElementById('collaboration-modal').style.display = 'none';
    });

    copySessionCodeBtn.addEventListener('click', () => {
        const sessionCode = document.getElementById('host-session-code').value;
        copyToClipboard(sessionCode, 'Session code copied! Share with your team.');
    });
}

function setupJoinPanelListeners() {
    const connectSessionBtn = document.getElementById('connect-session');
    const cancelJoinBtn = document.getElementById('cancel-join');

    connectSessionBtn.addEventListener('click', async () => {
        const sessionCode = document.getElementById('join-session-code').value.trim().toUpperCase();
        const joinStatus = document.getElementById('join-status');

        if (!sessionCode || sessionCode.length !== 6) {
            joinStatus.textContent = 'Please enter a valid 6-character session code.';
            return;
        }

        try {
            joinStatus.textContent = 'Connecting to session...';
            connectSessionBtn.disabled = true;

            await webrtcManager.joinSession(sessionCode);

            joinStatus.textContent = 'Connected successfully!';

            // Update collaborate button
            updateCollaborateButton('connected');
            isCollaborating = true;

            // Close modal
            setTimeout(() => {
                document.getElementById('collaboration-modal').style.display = 'none';
            }, 1000);

        } catch (error) {
            console.error('Failed to join session:', error);
            joinStatus.textContent = 'Failed to connect. Please check the code and try again.';
            connectSessionBtn.disabled = false;
        }
    });

    cancelJoinBtn.addEventListener('click', () => {
        document.getElementById('collaboration-modal').style.display = 'none';
    });
}

function updateCollaborateButton(status) {
    const collaborateBtn = document.getElementById('collaborate-btn');
    const sessionInfo = document.getElementById('session-info');
    const currentSessionCode = document.getElementById('current-session-code');

    switch (status) {
        case 'hosting':
            collaborateBtn.innerHTML = '<span class="tooltip">Managing Session</span>🏠 Hosting';
            collaborateBtn.classList.add('hosting');
            collaborateBtn.classList.remove('connected');

            // Show session info
            if (webrtcManager && webrtcManager.sessionCode) {
                currentSessionCode.textContent = webrtcManager.sessionCode;
                sessionInfo.style.display = 'flex';
            }
            break;

        case 'connected':
            collaborateBtn.innerHTML = '<span class="tooltip">Connected to Session</span>🔗 Connected';
            collaborateBtn.classList.add('connected');
            collaborateBtn.classList.remove('hosting');

            // Show session info
            if (webrtcManager && webrtcManager.sessionCode) {
                currentSessionCode.textContent = webrtcManager.sessionCode;
                sessionInfo.style.display = 'flex';
            }
            break;

        default:
            collaborateBtn.innerHTML = '<span class="tooltip">Start Collaboration</span>👥 Collaborate';
            collaborateBtn.classList.remove('hosting', 'connected');

            // Hide session info
            sessionInfo.style.display = 'none';
    }
}

// Helper function for copying to clipboard
function copyToClipboard(text, successMessage = 'Copied to clipboard!') {
    if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(text)
            .then(() => {
                showNotification(successMessage, 'success');
            })
            .catch(() => {
                fallbackCopy(text, successMessage);
            });
    } else {
        fallbackCopy(text, successMessage);
    }
}

function fallbackCopy(text, successMessage = 'Copied to clipboard!') {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();

    try {
        const success = document.execCommand('copy');
        if (success) {
            showNotification(successMessage, 'success');
        } else {
            showNotification('Please copy the code manually.', 'error');
        }
    } catch (err) {
        console.error('Fallback copy failed:', err);
        showNotification('Please copy the code manually.', 'error');
    }

    document.body.removeChild(textArea);
}

// Show notification function
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;

    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: ${type === 'success' ? '#28A745' : type === 'error' ? '#DC3545' : '#17A2B8'};
        color: white;
        padding: 12px 20px;
        border-radius: 6px;
        z-index: 10001;
        font-weight: 500;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        animation: slideIn 0.3s ease-out;
    `;

    // Add animation keyframes if not already added
    if (!document.querySelector('#notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Initialize the app when the DOM is loaded
document.addEventListener('DOMContentLoaded', init);