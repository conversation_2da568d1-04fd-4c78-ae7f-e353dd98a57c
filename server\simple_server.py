"""
Simple WebSocket Server for Valorant Strategy Board
Using websockets library for better compatibility
"""

import asyncio
import websockets
import json
import logging
from datetime import datetime
import random
import string
from typing import Dict, Set

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleCollaborationServer:
    def __init__(self):
        self.sessions: Dict[str, Dict] = {}
        self.clients: Dict[str, Dict] = {}  # websocket -> client info
        
    def generate_session_code(self) -> str:
        """Generate a unique 6-character session code"""
        chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'
        while True:
            code = ''.join(random.choices(chars, k=6))
            if code not in self.sessions:
                return code
    
    async def register_client(self, websocket, client_id: str):
        """Register a new client"""
        self.clients[websocket] = {
            'client_id': client_id,
            'user_id': None,
            'user_name': None,
            'session_code': None,
            'connected_at': datetime.now().isoformat()
        }
        logger.info(f"Client registered: {client_id}")
    
    async def unregister_client(self, websocket):
        """Unregister a client"""
        if websocket in self.clients:
            client_info = self.clients[websocket]
            session_code = client_info.get('session_code')
            
            # Remove from session if they were in one
            if session_code and session_code in self.sessions:
                await self.leave_session(websocket, session_code)
            
            del self.clients[websocket]
            logger.info(f"Client unregistered: {client_info.get('client_id')}")
    
    async def create_session(self, websocket, data):
        """Create a new collaboration session"""
        try:
            session_name = data.get('session_name', 'Strategy Session')
            user_name = data.get('user_name', 'Unknown User')
            user_id = data.get('user_id', f'user_{datetime.now().timestamp()}')
            
            session_code = self.generate_session_code()
            
            # Create session
            self.sessions[session_code] = {
                'session_name': session_name,
                'host_user_id': user_id,
                'host_user_name': user_name,
                'created_at': datetime.now().isoformat(),
                'users': {
                    user_id: {
                        'user_id': user_id,
                        'user_name': user_name,
                        'websocket': websocket,
                        'joined_at': datetime.now().isoformat(),
                        'is_host': True
                    }
                },
                'last_activity': datetime.now().isoformat()
            }
            
            # Update client info
            if websocket in self.clients:
                self.clients[websocket].update({
                    'user_id': user_id,
                    'user_name': user_name,
                    'session_code': session_code
                })
            
            # Send response
            response = {
                'type': 'session_created',
                'session_code': session_code,
                'session_name': session_name,
                'user_id': user_id,
                'user_name': user_name,
                'is_host': True
            }
            
            await websocket.send(json.dumps(response))
            logger.info(f"Session created: {session_code} by {user_name}")
            
        except Exception as e:
            logger.error(f"Error creating session: {e}")
            await self.send_error(websocket, str(e))
    
    async def join_session(self, websocket, data):
        """Join an existing session"""
        try:
            session_code = data.get('session_code', '').upper()
            user_name = data.get('user_name', 'Unknown User')
            user_id = data.get('user_id', f'user_{datetime.now().timestamp()}')
            
            if not session_code or len(session_code) != 6:
                raise ValueError("Invalid session code")
            
            if session_code not in self.sessions:
                raise ValueError("Session not found")
            
            session = self.sessions[session_code]
            
            # Add user to session
            session['users'][user_id] = {
                'user_id': user_id,
                'user_name': user_name,
                'websocket': websocket,
                'joined_at': datetime.now().isoformat(),
                'is_host': False
            }
            
            # Update client info
            if websocket in self.clients:
                self.clients[websocket].update({
                    'user_id': user_id,
                    'user_name': user_name,
                    'session_code': session_code
                })
            
            # Send response to joining user
            response = {
                'type': 'session_joined',
                'session_code': session_code,
                'session_name': session['session_name'],
                'user_id': user_id,
                'user_name': user_name,
                'is_host': False,
                'connected_users': [
                    {
                        'user_id': u['user_id'],
                        'user_name': u['user_name'],
                        'joined_at': u['joined_at'],
                        'is_host': u['is_host']
                    }
                    for u in session['users'].values()
                ]
            }
            
            await websocket.send(json.dumps(response))
            
            # Notify other users
            user_joined_msg = {
                'type': 'user_joined',
                'user_id': user_id,
                'user_name': user_name,
                'joined_at': datetime.now().isoformat()
            }
            
            await self.broadcast_to_session(session_code, user_joined_msg, exclude_websocket=websocket)
            logger.info(f"User {user_name} joined session: {session_code}")
            
        except Exception as e:
            logger.error(f"Error joining session: {e}")
            await self.send_error(websocket, str(e))
    
    async def leave_session(self, websocket, session_code):
        """Leave a session"""
        try:
            if session_code not in self.sessions:
                return
            
            session = self.sessions[session_code]
            client_info = self.clients.get(websocket, {})
            user_id = client_info.get('user_id')
            user_name = client_info.get('user_name')
            
            if user_id and user_id in session['users']:
                # Remove user from session
                del session['users'][user_id]
                
                # Notify other users
                user_left_msg = {
                    'type': 'user_left',
                    'user_id': user_id,
                    'user_name': user_name,
                    'left_at': datetime.now().isoformat()
                }
                
                await self.broadcast_to_session(session_code, user_left_msg)
                
                # Clean up empty session
                if not session['users']:
                    del self.sessions[session_code]
                    logger.info(f"Cleaned up empty session: {session_code}")
                
                logger.info(f"User {user_name} left session: {session_code}")
            
        except Exception as e:
            logger.error(f"Error leaving session: {e}")
    
    async def handle_canvas_change(self, websocket, data):
        """Handle canvas changes and broadcast to session"""
        try:
            client_info = self.clients.get(websocket, {})
            session_code = client_info.get('session_code')

            logger.info(f"Handling canvas change from {client_info.get('user_name')} in session {session_code}")
            logger.info(f"Canvas change data: {data}")

            if not session_code:
                logger.warning("No session code found for canvas change")
                return

            # Add metadata
            change_data = {
                **data,
                'from_user_id': client_info.get('user_id'),
                'from_user_name': client_info.get('user_name'),
                'timestamp': datetime.now().isoformat()
            }

            logger.info(f"Broadcasting canvas change to session {session_code}")
            # Broadcast to session
            await self.broadcast_to_session(session_code, change_data, exclude_websocket=websocket)

        except Exception as e:
            logger.error(f"Error handling canvas change: {e}")
    
    async def handle_cursor_update(self, websocket, data):
        """Handle cursor updates"""
        try:
            client_info = self.clients.get(websocket, {})
            session_code = client_info.get('session_code')
            
            if not session_code:
                return
            
            # Add user info
            cursor_data = {
                **data,
                'type': 'cursor_update',
                'user_id': client_info.get('user_id'),
                'user_name': client_info.get('user_name'),
                'timestamp': datetime.now().isoformat()
            }
            
            # Broadcast to session
            await self.broadcast_to_session(session_code, cursor_data, exclude_websocket=websocket)
            
        except Exception as e:
            logger.error(f"Error handling cursor update: {e}")
    
    async def broadcast_to_session(self, session_code, message, exclude_websocket=None):
        """Broadcast message to all users in a session"""
        if session_code not in self.sessions:
            return
        
        session = self.sessions[session_code]
        message_str = json.dumps(message)
        
        for user_data in session['users'].values():
            websocket = user_data['websocket']
            if websocket != exclude_websocket:
                try:
                    await websocket.send(message_str)
                except Exception as e:
                    logger.error(f"Error sending message to user: {e}")
    
    async def send_error(self, websocket, message):
        """Send error message to client"""
        error_msg = {
            'type': 'error',
            'message': message
        }
        try:
            await websocket.send(json.dumps(error_msg))
        except Exception as e:
            logger.error(f"Error sending error message: {e}")
    
    async def handle_message(self, websocket, message):
        """Handle incoming WebSocket message"""
        try:
            data = json.loads(message)
            message_type = data.get('type')
            
            if message_type == 'create_session':
                await self.create_session(websocket, data)
            elif message_type == 'join_session':
                await self.join_session(websocket, data)
            elif message_type == 'leave_session':
                session_code = data.get('session_code') or self.clients.get(websocket, {}).get('session_code')
                if session_code:
                    await self.leave_session(websocket, session_code)
            elif message_type == 'canvas_change' or message_type == 'canvas-change':
                await self.handle_canvas_change(websocket, data)
            elif message_type == 'cursor_update' or message_type == 'cursor-update':
                await self.handle_cursor_update(websocket, data)
            elif message_type == 'map_change' or message_type == 'map-change':
                await self.handle_canvas_change(websocket, data)  # Treat map changes like canvas changes
            elif message_type == 'canvas-sync-request' or message_type == 'canvas_sync_request':
                await self.handle_canvas_change(websocket, data)  # Treat sync requests like canvas changes
            elif message_type == 'canvas-sync-data' or message_type == 'canvas_sync_data':
                await self.handle_canvas_change(websocket, data)  # Treat sync data like canvas changes
            else:
                logger.warning(f"Unknown message type: {message_type}")
                
        except json.JSONDecodeError:
            logger.error("Invalid JSON received")
            await self.send_error(websocket, "Invalid JSON format")
        except Exception as e:
            logger.error(f"Error handling message: {e}")
            await self.send_error(websocket, str(e))

# Global server instance
server = SimpleCollaborationServer()

async def handle_client(websocket):
    """Handle WebSocket client connection"""
    client_id = f"client_{datetime.now().timestamp()}"

    try:
        await server.register_client(websocket, client_id)

        # Send connection confirmation
        await websocket.send(json.dumps({
            'type': 'connected',
            'client_id': client_id
        }))

        async for message in websocket:
            await server.handle_message(websocket, message)

    except websockets.exceptions.ConnectionClosed:
        logger.info(f"Client {client_id} disconnected")
    except Exception as e:
        logger.error(f"Error handling client {client_id}: {e}")
    finally:
        await server.unregister_client(websocket)

async def main():
    """Start the WebSocket server"""
    port = 8000
    logger.info(f"Starting WebSocket server on port {port}")
    
    async with websockets.serve(handle_client, "localhost", port):
        logger.info(f"Server running on ws://localhost:{port}")
        await asyncio.Future()  # Run forever

if __name__ == "__main__":
    asyncio.run(main())
