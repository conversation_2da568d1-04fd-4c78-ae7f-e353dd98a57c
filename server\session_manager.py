"""
Session Manager for Valorant Strategy Board
Handles session creation, user management, and session state
"""

import random
import string
from typing import Dict, List, Optional
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class SessionManager:
    def __init__(self):
        self.sessions: Dict[str, Dict] = {}
    
    def generate_session_code(self) -> str:
        """Generate a unique 6-character session code"""
        chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'  # Exclude confusing characters
        while True:
            code = ''.join(random.choices(chars, k=6))
            if code not in self.sessions:
                return code
    
    def create_session(self, session_name: str, host_sid: str, host_user_id: str, host_user_name: str) -> str:
        """Create a new collaboration session"""
        session_code = self.generate_session_code()
        
        self.sessions[session_code] = {
            'session_name': session_name,
            'host_sid': host_sid,
            'host_user_id': host_user_id,
            'host_user_name': host_user_name,
            'created_at': datetime.now().isoformat(),
            'users': {
                host_user_id: {
                    'user_id': host_user_id,
                    'user_name': host_user_name,
                    'sid': host_sid,
                    'joined_at': datetime.now().isoformat(),
                    'is_host': True
                }
            },
            'canvas_state': None,  # Store latest canvas state for new joiners
            'last_activity': datetime.now().isoformat()
        }
        
        logger.info(f"Created session {session_code}: {session_name}")
        return session_code
    
    def session_exists(self, session_code: str) -> bool:
        """Check if a session exists"""
        return session_code in self.sessions
    
    def join_session(self, session_code: str, user_sid: str, user_id: str, user_name: str) -> bool:
        """Add a user to an existing session"""
        if session_code not in self.sessions:
            raise ValueError(f"Session {session_code} does not exist")
        
        session = self.sessions[session_code]
        
        # Check if user is already in session
        if user_id in session['users']:
            # Update their SID in case they reconnected
            session['users'][user_id]['sid'] = user_sid
            session['users'][user_id]['rejoined_at'] = datetime.now().isoformat()
        else:
            # Add new user
            session['users'][user_id] = {
                'user_id': user_id,
                'user_name': user_name,
                'sid': user_sid,
                'joined_at': datetime.now().isoformat(),
                'is_host': False
            }
        
        session['last_activity'] = datetime.now().isoformat()
        
        logger.info(f"User {user_name} joined session {session_code}")
        return True
    
    def leave_session(self, session_code: str, user_sid: str) -> bool:
        """Remove a user from a session"""
        if session_code not in self.sessions:
            return False
        
        session = self.sessions[session_code]
        
        # Find user by SID
        user_to_remove = None
        for user_id, user_data in session['users'].items():
            if user_data['sid'] == user_sid:
                user_to_remove = user_id
                break
        
        if user_to_remove:
            user_name = session['users'][user_to_remove]['user_name']
            is_host = session['users'][user_to_remove]['is_host']
            
            # Remove user
            del session['users'][user_to_remove]
            session['last_activity'] = datetime.now().isoformat()
            
            logger.info(f"User {user_name} left session {session_code}")
            
            # If host left and there are other users, promote someone to host
            if is_host and session['users']:
                new_host_id = next(iter(session['users']))
                session['users'][new_host_id]['is_host'] = True
                session['host_sid'] = session['users'][new_host_id]['sid']
                session['host_user_id'] = new_host_id
                session['host_user_name'] = session['users'][new_host_id]['user_name']
                logger.info(f"Promoted {session['host_user_name']} to host of session {session_code}")
            
            # If no users left, clean up session
            elif not session['users']:
                self.cleanup_session(session_code)
            
            return True
        
        return False
    
    def cleanup_session(self, session_code: str):
        """Remove empty session"""
        if session_code in self.sessions:
            session_name = self.sessions[session_code]['session_name']
            del self.sessions[session_code]
            logger.info(f"Cleaned up empty session {session_code}: {session_name}")
    
    def get_session_info(self, session_code: str) -> Optional[Dict]:
        """Get session information"""
        if session_code not in self.sessions:
            return None
        
        session = self.sessions[session_code]
        return {
            'session_code': session_code,
            'session_name': session['session_name'],
            'host_user_id': session['host_user_id'],
            'host_user_name': session['host_user_name'],
            'created_at': session['created_at'],
            'last_activity': session['last_activity'],
            'user_count': len(session['users']),
            'users': [
                {
                    'user_id': user_data['user_id'],
                    'user_name': user_data['user_name'],
                    'joined_at': user_data['joined_at'],
                    'is_host': user_data['is_host']
                }
                for user_data in session['users'].values()
            ]
        }
    
    def get_user_session(self, user_sid: str) -> Optional[str]:
        """Find which session a user is in"""
        for session_code, session in self.sessions.items():
            for user_data in session['users'].values():
                if user_data['sid'] == user_sid:
                    return session_code
        return None
    
    def update_canvas_state(self, session_code: str, canvas_state: Dict):
        """Update the canvas state for a session"""
        if session_code in self.sessions:
            self.sessions[session_code]['canvas_state'] = canvas_state
            self.sessions[session_code]['last_activity'] = datetime.now().isoformat()
    
    def get_canvas_state(self, session_code: str) -> Optional[Dict]:
        """Get the current canvas state for a session"""
        if session_code in self.sessions:
            return self.sessions[session_code]['canvas_state']
        return None
    
    def get_all_sessions(self) -> List[Dict]:
        """Get information about all active sessions"""
        return [
            {
                'session_code': session_code,
                'session_name': session['session_name'],
                'user_count': len(session['users']),
                'created_at': session['created_at'],
                'last_activity': session['last_activity']
            }
            for session_code, session in self.sessions.items()
        ]
    
    def cleanup_inactive_sessions(self, max_inactive_hours: int = 24):
        """Clean up sessions that have been inactive for too long"""
        from datetime import datetime, timedelta
        
        cutoff_time = datetime.now() - timedelta(hours=max_inactive_hours)
        sessions_to_remove = []
        
        for session_code, session in self.sessions.items():
            last_activity = datetime.fromisoformat(session['last_activity'])
            if last_activity < cutoff_time:
                sessions_to_remove.append(session_code)
        
        for session_code in sessions_to_remove:
            session_name = self.sessions[session_code]['session_name']
            del self.sessions[session_code]
            logger.info(f"Cleaned up inactive session {session_code}: {session_name}")
        
        return len(sessions_to_remove)
