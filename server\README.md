# Valorant Strategy Board - Collaboration Server

This is the Python server component that replaces WebRTC for better browser compatibility. The server handles real-time collaboration using WebSockets.

## Features

- **Session Management**: Create and join collaboration sessions with 6-character codes
- **Real-time Synchronization**: Canvas changes, cursor positions, and map changes
- **User Presence**: Track connected users and handle join/leave events
- **Cross-browser Compatibility**: Works with all modern browsers
- **Scalable**: Can handle multiple concurrent sessions

## Local Development

### Prerequisites

- Python 3.11 or higher
- pip (Python package manager)

### Setup

1. Navigate to the server directory:
   ```bash
   cd server
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Run the server:
   ```bash
   python main.py
   ```

The server will start on `http://localhost:8000`

### Testing

You can test the server by opening multiple browser tabs to your frontend application. The WebSocket manager will automatically connect to `localhost:8000` when running locally.

## Deployment to Render.com

### Step 1: Prepare Your Repository

1. Make sure all server files are in the `server/` directory
2. Ensure `render.yaml` is in the root of your repository
3. Commit and push your changes to GitHub

### Step 2: Deploy on Render

1. Go to [Render.com](https://render.com) and sign up/login
2. Click "New +" and select "Web Service"
3. Connect your GitHub repository
4. Render will automatically detect the `render.yaml` configuration
5. Click "Create Web Service"

### Step 3: Update Frontend Configuration

1. Once deployed, Render will give you a URL like `https://your-app-name.onrender.com`
2. Update the `getServerUrl()` method in `js/websocket-manager.js`:
   ```javascript
   getServerUrl() {
       const hostname = window.location.hostname;
       
       if (hostname === 'localhost' || hostname === '127.0.0.1') {
           return 'http://localhost:8000';
       } else {
           // Replace with your actual Render URL
           return 'https://your-app-name.onrender.com';
       }
   }
   ```

### Step 4: Test the Deployment

1. Open your frontend application
2. Try creating and joining sessions
3. Test real-time collaboration features

## API Endpoints

### HTTP Endpoints

- `GET /` - Server status and health check
- `GET /health` - Health check for deployment platforms

### WebSocket Events

#### Client to Server

- `create_session` - Create a new collaboration session
- `join_session` - Join an existing session
- `leave_session` - Leave current session
- `canvas_change` - Broadcast canvas changes
- `cursor_update` - Send cursor position updates

#### Server to Client

- `session_created` - Session creation confirmation
- `session_joined` - Session join confirmation
- `user_joined` - New user joined the session
- `user_left` - User left the session
- `canvas_change` - Canvas change from another user
- `cursor_update` - Cursor position from another user
- `error` - Error messages

## Configuration

### Environment Variables

- `PORT` - Server port (default: 8000)
- `PYTHON_VERSION` - Python version for deployment

### CORS Settings

The server is configured to allow all origins for development. In production, you should update the CORS settings in `main.py` to only allow your domain:

```python
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://yourdomain.com"],  # Replace with your domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

## Monitoring

### Logs

The server provides detailed logging for:
- Session creation and management
- User connections and disconnections
- Canvas synchronization events
- Error handling

### Health Checks

- The `/health` endpoint provides server status
- Render.com automatically monitors this endpoint

## Troubleshooting

### Common Issues

1. **Connection Failed**: Check if the server URL in `websocket-manager.js` is correct
2. **CORS Errors**: Ensure CORS is properly configured for your domain
3. **Session Not Found**: Sessions are stored in memory and will be lost on server restart

### Development Tips

1. Use browser developer tools to monitor WebSocket connections
2. Check server logs for detailed error information
3. Test with multiple browser tabs to simulate multiple users

## Scaling Considerations

For production use with many concurrent users, consider:

1. **Database Storage**: Replace in-memory session storage with Redis or a database
2. **Load Balancing**: Use multiple server instances with sticky sessions
3. **Rate Limiting**: Implement rate limiting for API endpoints
4. **Monitoring**: Add application monitoring and alerting
