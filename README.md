# Valorant Strategy Board - Real-time Collaboration Tool

A professional, real-time collaborative strategy board for Valorant teams. Plan tactics, draw strategies, and coordinate with your team on all Valorant maps with live synchronization.

## 🚀 New Features

### ✨ Real-time Collaboration
- **WebRTC-powered**: Peer-to-peer connections for low-latency collaboration
- **Live synchronization**: See changes from teammates in real-time
- **User presence**: Track who's connected and active
- **Session management**: Easy-to-use host/join system with 6-character codes

### 🎨 Enhanced Editor
- **Fixed z-index issues**: Drawing tools and crosshair now work perfectly
- **Improved precision**: Better tool responsiveness and visual feedback
- **Enhanced eraser**: Smart eraser that preserves agent icons
- **Better layer management**: Proper stacking and interaction handling

### 🔍 SEO Optimized
- **Comprehensive meta tags**: Open Graph, Twitter Cards, and structured data
- **Search engine friendly**: Proper heading hierarchy and semantic HTML
- **Performance optimized**: Fast loading and responsive design
- **Accessibility features**: Screen reader friendly with proper ARIA labels

## 🎮 Core Features

### Interactive Map System
- Choose from all Valorant maps (Ascent, Bind, <PERSON>, Split, Icebox, Pearl, Fracture, Lotus, Sunset, and Abyss)
- High-quality map images serve as the base layer for strategy planning
- Zoom and pan functionality for detailed planning

### Agent Placement
- Drag and drop all current Valorant agent icons onto the map
- Rotate and resize agents using intuitive controls
- Smart positioning with visual feedback

### Advanced Drawing Tools
- Pencil tool for freehand drawing with smooth curves
- Line and arrow tools for movement paths and callouts
- Rectangle and circle tools for marking areas and zones
- Multiple colors for different types of annotations
- Smart eraser that preserves agent icons
- Adjustable brush sizes for precise control

### Strategy Management
- Save strategies with custom names to local storage
- Load existing strategies instantly
- Delete unwanted strategies with confirmation
- Comprehensive undo/redo functionality (20 levels)
- Clear canvas option with confirmation

## 🚀 Getting Started

### Basic Usage
1. **Select a Map**: Choose your desired map from the dropdown at the top
2. **Place Agents**: Click on agent icons in the sidebar to add them to the map
3. **Draw Strategy Elements**:
   - Use the pencil tool for freehand drawing
   - Use line and arrow tools for movement paths
   - Use shape tools (rectangle, circle) to highlight areas
   - Change colors using the color picker
   - Adjust brush size with the slider
4. **Save Your Strategy**: Enter a name and click Save
5. **Load Strategies**: Click Load to access saved strategies

### 🤝 Real-time Collaboration
1. **Click the "👥 Collaborate" button** in the header
2. **Choose your mode**:
   - **Host Session**: Create a new collaboration session
     - Enter a session name (optional)
     - Click "Start Session"
     - Share the 6-character code with your team
   - **Join Session**: Connect to an existing session
     - Enter the 6-character code from your teammate
     - Click "Connect"
3. **Start collaborating**: All changes are synchronized in real-time!

### ⌨️ Keyboard Shortcuts
| Key | Action |
|-----|--------|
| `V` | Select tool |
| `P` | Pencil tool |
| `L` | Line tool |
| `A` | Arrow tool |
| `R` | Rectangle tool |
| `C` | Circle tool |
| `E` | Eraser tool |
| `Del/Backspace` | Delete selected object |
| `Ctrl+Z` | Undo |
| `Ctrl+Y` | Redo |
| `Ctrl++` | Zoom in |
| `Ctrl+-` | Zoom out |
| `Ctrl+0` | Reset zoom |

## 🛠️ Technical Stack

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Canvas**: Fabric.js for high-performance drawing and manipulation
- **WebRTC**: PeerJS for real-time peer-to-peer collaboration
- **Storage**: Browser localStorage for strategy persistence
- **Styling**: Custom CSS with Valorant-inspired design theme
- **SEO**: Comprehensive meta tags, structured data, and sitemap

## 📁 Project Structure

```
Strategy-board/
├── index.html              # Main application
├── css/
│   └── styles.css          # Application styles with collaboration UI
├── js/
│   ├── app.js             # Main application logic
│   ├── webrtc-manager.js  # WebRTC connection management
│   └── collaboration.js   # Real-time collaboration features
├── mini-map/              # Valorant map images
├── agent-icons/           # Valorant agent icons
├── tictactoe/            # Example WebRTC implementation
├── sitemap.xml           # SEO sitemap
├── robots.txt            # Search engine directives
└── README.md             # Documentation
```

## 🔧 Development

### Local Development
1. Clone this repository
2. Open `index.html` in your web browser
3. For collaboration testing, open multiple browser tabs/windows
4. No server or build process required - this is a client-side application

### Browser Compatibility
- **Chrome 60+** (recommended for best WebRTC support)
- **Firefox 55+**
- **Safari 11+**
- **Edge 79+**

### WebRTC Requirements
- Modern browser with WebRTC support
- Internet connection for initial peer discovery
- HTTPS required for production deployment

## 🌐 Deployment

### SEO Setup
1. Update domain references in `index.html` meta tags
2. Replace `yourdomain.com` with your actual domain
3. Update `sitemap.xml` with your domain
4. Add favicon files to the root directory

### Production Considerations
- Deploy over HTTPS for WebRTC functionality
- Configure STUN/TURN servers for better connectivity
- Optimize images for faster loading
- Set up proper caching headers

## 🎯 Supported Valorant Maps

- ✅ Ascent
- ✅ Bind
- ✅ Haven
- ✅ Split
- ✅ Icebox
- ✅ Pearl
- ✅ Fracture
- ✅ Lotus
- ✅ Sunset
- ✅ Abyss

## 🔮 Future Enhancements

- Voice chat integration
- Strategy templates and presets
- Team management system
- Cloud storage and sharing
- Mobile app version
- Advanced drawing tools
- Strategy replay system
- Tournament mode

---

**Built for the Valorant community** 🎮
*Plan better, play better, win together!*