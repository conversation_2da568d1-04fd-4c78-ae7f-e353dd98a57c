# Deployment Guide - Server-Based Collaboration

This guide will help you deploy the new server-based collaboration system to replace WebRTC.

## What Changed

✅ **Replaced WebRTC** with a Python WebSocket server for better browser compatibility  
✅ **Added server-side session management** for reliable connections  
✅ **Maintained the same user interface** - no changes to the user experience  
✅ **Improved reliability** across different browsers and network configurations  

## Quick Start

### 1. Deploy the Server to Render.com

1. **Push your code to GitHub** (if not already done)
   ```bash
   git add .
   git commit -m "Add server-based collaboration"
   git push origin main
   ```

2. **Go to [Render.com](https://render.com)** and sign up/login

3. **Create a new Web Service**:
   - Click "New +" → "Web Service"
   - Connect your GitHub repository
   - Render will auto-detect the `render.yaml` configuration
   - Click "Create Web Service"

4. **Wait for deployment** (usually 2-3 minutes)
   - Render will give you a URL like `https://your-app-name.onrender.com`

### 2. Update Your Frontend

1. **Update the server URL** in `js/websocket-manager.js`:
   ```javascript
   // Replace 'your-app-name' with your actual Render app name
   return 'https://your-app-name.onrender.com';
   ```

2. **Deploy your frontend** (GitHub Pages, Netlify, Vercel, etc.)

### 3. Test the System

1. Open your website in multiple browser tabs
2. Create a session in one tab
3. Join the session from another tab using the session code
4. Test drawing, moving objects, and changing maps

## Detailed Steps

### Server Deployment

The server is already configured for Render.com deployment:

- **`render.yaml`** - Deployment configuration
- **`server/requirements.txt`** - Python dependencies
- **`server/main.py`** - Main server application
- **Health checks** - Automatic monitoring

### Frontend Updates

The frontend has been updated to use WebSockets instead of WebRTC:

- **`js/websocket-manager.js`** - New WebSocket-based connection manager
- **`js/collaboration.js`** - Updated to work with WebSocket manager
- **`index.html`** - Added Socket.IO client library

### Configuration Options

#### Custom Server URL
You can set a custom server URL by modifying `getServerUrl()` in `websocket-manager.js`:

```javascript
getServerUrl() {
    // Option 1: Use environment-based detection (current)
    const hostname = window.location.hostname;
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
        return 'http://localhost:8000';
    } else {
        return 'https://your-server-url.com';
    }
    
    // Option 2: Always use a specific URL
    // return 'https://your-server-url.com';
    
    // Option 3: Use different URLs for different environments
    // if (hostname.includes('staging')) {
    //     return 'https://staging-server.com';
    // } else {
    //     return 'https://production-server.com';
    // }
}
```

## Local Development

### Running the Server Locally

1. **Install Python dependencies**:
   ```bash
   cd server
   pip install -r requirements.txt
   ```

2. **Start the server**:
   ```bash
   python main.py
   ```
   Server will run on `http://localhost:8000`

3. **Open your frontend** in a browser
   The WebSocket manager will automatically connect to localhost

### Testing Locally

1. Open multiple browser tabs to your frontend
2. Create and join sessions
3. Test all collaboration features

## Troubleshooting

### Common Issues

**❌ "Connection failed" error**
- Check if the server URL in `websocket-manager.js` is correct
- Verify the server is running and accessible
- Check browser console for detailed error messages

**❌ "Session not found" error**
- Sessions are stored in memory and reset when server restarts
- Make sure both users are connecting to the same server

**❌ CORS errors**
- The server is configured to allow all origins for development
- For production, update CORS settings in `server/main.py`

**❌ Slow connections**
- Render.com free tier may have cold starts (first request takes longer)
- Consider upgrading to a paid plan for better performance

### Debug Tips

1. **Check browser console** for WebSocket connection status
2. **Monitor server logs** on Render.com dashboard
3. **Test with different browsers** to ensure compatibility
4. **Use browser dev tools** to inspect WebSocket messages

## Performance Considerations

### Free Tier Limitations
- Render.com free tier has some limitations:
  - Cold starts (server sleeps after 15 minutes of inactivity)
  - Limited resources
  - Shared infrastructure

### Optimization Tips
1. **Keep sessions active** - Regular activity prevents cold starts
2. **Monitor usage** - Check Render.com dashboard for performance metrics
3. **Consider upgrading** - Paid plans offer better performance and reliability

## Security Notes

### Current Configuration
- Server allows all origins (for development ease)
- No authentication required
- Sessions are public with 6-character codes

### Production Recommendations
1. **Update CORS settings** to only allow your domain
2. **Add rate limiting** to prevent abuse
3. **Consider authentication** for private sessions
4. **Use HTTPS** for all connections (Render.com provides this automatically)

## Next Steps

After successful deployment:

1. **Test thoroughly** with multiple users
2. **Monitor performance** and error rates
3. **Gather user feedback** on the new system
4. **Consider additional features** like:
   - Session passwords
   - User authentication
   - Persistent sessions
   - Advanced admin controls

## Support

If you encounter issues:

1. Check the server logs on Render.com
2. Review browser console errors
3. Test with the local development setup
4. Verify all configuration settings

The new system should provide much better browser compatibility and reliability compared to the previous WebRTC implementation!
