/**
 * WebRTC Manager for Valorant Strategy Board
 * Handles peer-to-peer connections and real-time synchronization
 */

class WebRTCManager {
    constructor() {
        this.peer = null;
        this.connections = new Map(); // Map of peer IDs to connection objects
        this.isHost = false;
        this.sessionCode = null;
        this.sessionName = '';
        this.localUserId = this.generateUserId();
        this.localUserName = 'User_' + this.localUserId.substring(0, 6);
        
        // Event callbacks
        this.onConnectionEstablished = null;
        this.onConnectionLost = null;
        this.onDataReceived = null;
        this.onUserJoined = null;
        this.onUserLeft = null;
        this.onError = null;
        
        console.log('WebRTC Manager initialized with user ID:', this.localUserId);
    }
    
    /**
     * Generate a unique user ID
     */
    generateUserId() {
        return 'user_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now().toString(36);
    }
    
    /**
     * Generate a 6-character session code
     */
    generateSessionCode() {
        const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
        let code = '';
        for (let i = 0; i < 6; i++) {
            code += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return code;
    }
    
    /**
     * Initialize as host and create a new session
     */
    async createSession(sessionName = 'Strategy Session') {
        try {
            this.sessionName = sessionName;
            this.isHost = true;
            this.sessionCode = this.generateSessionCode();
            
            console.log('Creating session with code:', this.sessionCode);
            
            // Create peer with session code as ID
            this.peer = new Peer(this.sessionCode, {
                debug: 1,
                config: {
                    iceServers: [
                        { urls: 'stun:stun.l.google.com:19302' },
                        { urls: 'stun:stun1.l.google.com:19302' }
                    ]
                }
            });
            
            return new Promise((resolve, reject) => {
                this.peer.on('open', (id) => {
                    console.log('Session created successfully with ID:', id);
                    this.setupHostEventHandlers();
                    resolve(this.sessionCode);
                });
                
                this.peer.on('error', (error) => {
                    console.error('Error creating session:', error);
                    if (this.onError) this.onError(error);
                    reject(error);
                });
            });
        } catch (error) {
            console.error('Failed to create session:', error);
            if (this.onError) this.onError(error);
            throw error;
        }
    }
    
    /**
     * Join an existing session
     */
    async joinSession(sessionCode) {
        try {
            this.sessionCode = sessionCode.toUpperCase();
            this.isHost = false;
            
            console.log('Joining session with code:', this.sessionCode);
            
            // Create peer with random ID
            this.peer = new Peer({
                debug: 1,
                config: {
                    iceServers: [
                        { urls: 'stun:stun.l.google.com:19302' },
                        { urls: 'stun:stun1.l.google.com:19302' }
                    ]
                }
            });
            
            return new Promise((resolve, reject) => {
                this.peer.on('open', (id) => {
                    console.log('Peer created with ID:', id);
                    this.connectToHost()
                        .then(() => resolve())
                        .catch(reject);
                });
                
                this.peer.on('error', (error) => {
                    console.error('Error joining session:', error);
                    if (this.onError) this.onError(error);
                    reject(error);
                });
            });
        } catch (error) {
            console.error('Failed to join session:', error);
            if (this.onError) this.onError(error);
            throw error;
        }
    }
    
    /**
     * Connect to the host as a client
     */
    async connectToHost() {
        return new Promise((resolve, reject) => {
            const connection = this.peer.connect(this.sessionCode, {
                reliable: true,
                serialization: 'json'
            });
            
            connection.on('open', () => {
                console.log('Connected to host');
                this.connections.set(this.sessionCode, connection);
                this.setupConnectionHandlers(connection);
                
                // Send join message
                this.sendToConnection(connection, {
                    type: 'user-join',
                    userId: this.localUserId,
                    userName: this.localUserName
                });
                
                if (this.onConnectionEstablished) {
                    this.onConnectionEstablished();
                }
                resolve();
            });
            
            connection.on('error', (error) => {
                console.error('Connection error:', error);
                reject(error);
            });
            
            // Set timeout for connection
            setTimeout(() => {
                if (!connection.open) {
                    reject(new Error('Connection timeout'));
                }
            }, 10000);
        });
    }
    
    /**
     * Set up event handlers for host
     */
    setupHostEventHandlers() {
        this.peer.on('connection', (connection) => {
            console.log('New connection from:', connection.peer);
            
            connection.on('open', () => {
                console.log('Connection established with:', connection.peer);
                this.connections.set(connection.peer, connection);
                this.setupConnectionHandlers(connection);
                
                if (this.onConnectionEstablished) {
                    this.onConnectionEstablished();
                }
            });
        });
    }
    
    /**
     * Set up event handlers for a connection
     */
    setupConnectionHandlers(connection) {
        connection.on('data', (data) => {
            console.log('Received data from', connection.peer, ':', data);
            this.handleIncomingData(data, connection);
        });
        
        connection.on('close', () => {
            console.log('Connection closed:', connection.peer);
            this.connections.delete(connection.peer);
            
            if (this.onConnectionLost) {
                this.onConnectionLost(connection.peer);
            }
        });
        
        connection.on('error', (error) => {
            console.error('Connection error with', connection.peer, ':', error);
            this.connections.delete(connection.peer);
            
            if (this.onError) {
                this.onError(error);
            }
        });
    }
    
    /**
     * Handle incoming data from peers
     */
    handleIncomingData(data, fromConnection) {
        if (this.onDataReceived) {
            this.onDataReceived(data, fromConnection.peer);
        }
        
        // If we're the host, relay data to other peers
        if (this.isHost && data.type !== 'user-join' && data.type !== 'user-leave') {
            this.relayDataToOthers(data, fromConnection.peer);
        }
    }
    
    /**
     * Relay data to all other connected peers (host only)
     */
    relayDataToOthers(data, excludePeerId) {
        this.connections.forEach((connection, peerId) => {
            if (peerId !== excludePeerId && connection.open) {
                this.sendToConnection(connection, data);
            }
        });
    }
    
    /**
     * Send data to a specific connection
     */
    sendToConnection(connection, data) {
        try {
            if (connection && connection.open) {
                connection.send(data);
                return true;
            }
            return false;
        } catch (error) {
            console.error('Error sending data:', error);
            return false;
        }
    }
    
    /**
     * Broadcast data to all connected peers
     */
    broadcast(data) {
        let sentCount = 0;
        this.connections.forEach((connection) => {
            if (this.sendToConnection(connection, data)) {
                sentCount++;
            }
        });
        console.log(`Broadcasted data to ${sentCount} peers`);
        return sentCount;
    }
    
    /**
     * Get list of connected peer IDs
     */
    getConnectedPeers() {
        return Array.from(this.connections.keys());
    }
    
    /**
     * Check if connected to any peers
     */
    isConnected() {
        return this.connections.size > 0;
    }
    
    /**
     * Disconnect from all peers and destroy the peer
     */
    disconnect() {
        console.log('Disconnecting from all peers...');
        
        // Send disconnect message
        this.broadcast({
            type: 'user-leave',
            userId: this.localUserId,
            userName: this.localUserName
        });
        
        // Close all connections
        this.connections.forEach((connection) => {
            connection.close();
        });
        this.connections.clear();
        
        // Destroy peer
        if (this.peer) {
            this.peer.destroy();
            this.peer = null;
        }
        
        // Reset state
        this.isHost = false;
        this.sessionCode = null;
        this.sessionName = '';
        
        console.log('Disconnected successfully');
    }
    
    /**
     * Get session information
     */
    getSessionInfo() {
        return {
            sessionCode: this.sessionCode,
            sessionName: this.sessionName,
            isHost: this.isHost,
            connectedPeers: this.getConnectedPeers().length,
            localUserId: this.localUserId,
            localUserName: this.localUserName
        };
    }
}

// Export for use in other files
window.WebRTCManager = WebRTCManager;
